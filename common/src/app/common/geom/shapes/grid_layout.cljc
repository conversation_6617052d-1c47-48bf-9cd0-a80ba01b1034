;; This Source Code Form is subject to the terms of the Mozilla Public
;; License, v. 2.0. If a copy of the MPL was not distributed with this
;; file, You can obtain one at http://mozilla.org/MPL/2.0/.
;;
;; Copyright (c) KALEIDOS INC

(ns app.common.geom.shapes.grid-layout
  (:require
   [app.common.data.macros :as dm]
   [app.common.geom.shapes.grid-layout.bounds :as glpb]
   [app.common.geom.shapes.grid-layout.layout-data :as glld]
   [app.common.geom.shapes.grid-layout.params :as glpr]
   [app.common.geom.shapes.grid-layout.positions :as glp]))

(dm/export glld/calc-layout-data)
(dm/export glld/get-cell-data)
(dm/export glp/child-modifiers)
(dm/export glp/get-position-grid-coord)
(dm/export glp/get-drop-cell)
(dm/export glp/cell-bounds)
(dm/export glpb/layout-content-points)
(dm/export glpb/layout-content-bounds)
(dm/export glpr/calculate-params)
