[{:id "tokens-starter-kit"
  :name "Design tokens starter kit"
  :file-uri "https://github.com/penpot/penpot-files/raw/refs/heads/main/Tokens%20starter%20kit.penpot"},
 {:id "wireframing-kit"
  :name "Wireframe library"
  :file-uri "https://github.com/penpot/penpot-files/raw/refs/heads/main/Wireframing%20kit%20v1.1.penpot"}
 {:id "prototype-examples"
  :name "Prototype template"
  :file-uri "https://github.com/penpot/penpot-files/raw/refs/heads/main/Prototype%20examples%20v1.1.penpot"}
 {:id "plants-app"
  :name "UI mockup example"
  :file-uri "https://github.com/penpot/penpot-files/raw/main/Plants-app.penpot"}
 {:id "penpot-design-system"
  :name "Design system example"
  :file-uri "https://github.com/penpot/penpot-files/raw/refs/heads/main/Penpot%20-%20Design%20System%20v2.1.penpot"}
 {:id "tutorial-for-beginners"
  :name "Tutorial for beginners"
  :file-uri "https://github.com/penpot/penpot-files/raw/main/tutorial-for-beginners.penpot"}
 {:id "lucide-icons"
  :name "Lucide Icons"
  :file-uri "https://github.com/penpot/penpot-files/raw/main/Lucide-icons.penpot"}
 {:id "font-awesome"
  :name "Font Awesome"
  :file-uri "https://github.com/penpot/penpot-files/raw/main/FontAwesome.penpot"}
 {:id "black-white-mobile-templates"
  :name "Black & White Mobile Templates"
  :file-uri "https://github.com/penpot/penpot-files/raw/main/Black-&-White-Mobile-Templates.penpot"}
 {:id "avataaars"
  :name "Avataaars"
  :file-uri "https://github.com/penpot/penpot-files/raw/main/Avataaars-by-Pablo-Stanley.penpot"}
 {:id "ux-notes"
  :name "UX Notes"
  :file-uri "https://github.com/penpot/penpot-files/raw/main/UX-Notes.penpot"}
 {:id "whiteboarding-kit"
  :name "Whiteboarding Kit"
  :file-uri "https://github.com/penpot/penpot-files/raw/main/Whiteboarding-mapping-kit.penpot"}
 {:id "open-color-scheme"
  :name "Open Color Scheme"
  :file-uri "https://github.com/penpot/penpot-files/raw/main/Open%20Color%20Scheme%20(v1.9.1).penpot"}
 {:id "flex-layout-playground"
  :name "Flex Layout Playground"
  :file-uri "https://github.com/penpot/penpot-files/raw/refs/heads/main/Flex%20Layout%20Playground%20v2.0.penpot"}
 {:id "welcome"
  :name "Welcome"
  :file-uri "https://github.com/penpot/penpot-files/raw/main/welcome.penpot"}]
