{% extends "app/templates/base.tmpl" %}

{% block title %}
Debug Main Page
{% endblock %}

{% block content %}
<nav>
  <div class="title">
    <h1>ADMIN DEBUG INTERFACE (VERSION: {{version}})</h1>
  </div>
</nav>
<main class="dashboard">
  <section class="widget">
    <fieldset>
      <legend>Error reports</legend>
      <desc><a href="/dbg/error">CLICK HERE TO SEE THE ERROR REPORTS</a> </desc>
    </fieldset>

    <fieldset>
      <legend>Profile Management</legend>
      <form method="post" action="/dbg/actions/resend-email-verification">
        <div class="row">
          <input type="email" name="email" placeholder="<EMAIL>" value="" />
        </div>

        <div class="row">
          <label for="force-verify">Are you sure?</label>
          <input id="force-verify" type="checkbox" name="force" />
          <br />
          <small>
            This is a just a security double check for prevent non intentional submits.
          </small>
        </div>

        <div class="row">
          <input type="submit" name="resend" value="Resend Verification" />
          <input type="submit" name="verify" value="Verify" />
        </div>

        <div class="row">
          <input type="submit" class="danger" name="block" value="Block" />
          <input type="submit" class="danger" name="unblock" value="Unblock" />
        </div>
      </form>
    </fieldset>



  </section>

  <section class="widget">

    <fieldset>
      <legend>Download RAW file data:</legend>
      <desc>Given an FILE-ID, downloads the file AS-IS (no validation
        checks, just exports the file data and related objects in raw)

        <br/>
        <br/>
        <b>WARNING: this operation does not performs any checks</b>
      </desc>
      <form method="get" action="/dbg/actions/file-raw-export-import">
        <div class="row">
          <input type="text" style="width:300px" name="file-id" placeholder="file-id" />
        </div>
        <div class="row">
          <input type="submit" name="download" value="Download" />
          <input type="submit" name="clone" value="Clone" />
        </div>
      </form>
    </fieldset>

    <fieldset>
      <legend>Upload File Data:</legend>
      <desc>Create a new file on your draft projects using the file downloaded from the previous section.
        <br/>
        <br/>
        <b>WARNING: this operation does not performs any checks</b>
      </desc>
      <form method="post" enctype="multipart/form-data" action="/dbg/actions/file-raw-export-import">
        <div class="row">
          <input type="file" name="file" value="" />
        </div>
        <div class="row">
          <label>Import with same id?</label>
          <input type="checkbox" name="reuseid" />
        </div>

        <div class="row">
          <input type="submit" value="Upload" />
        </div>
      </form>
    </fieldset>
  </section>
  <section class="widget">
    <fieldset>
      <legend>Export binfile:</legend>
      <desc>Given an FILE-ID, downloads the file and optionally all
      the related libraries in a single custom formatted binary
      file.</desc>

      <form method="get" action="/dbg/actions/file-export">
        <div class="row set-of-inputs">
          <input type="text" style="width:300px" name="file-ids" placeholder="file-id" />
          <input type="text" style="width:300px" name="file-ids" placeholder="file-id" />
          <input type="text" style="width:300px" name="file-ids" placeholder="file-id" />
          <input type="text" style="width:300px" name="file-ids" placeholder="file-id" />
        </div>

        <div class="row">
          <label>Include libraries?</label>
          <input type="checkbox" name="includelibs" />
        </div>

        <div class="row">
          <label>Embed assets?</label>
          <input type="checkbox" name="embedassets" checked/>
        </div>

        <div class="row">
          <input type="submit" name="download" value="Download" />
          <input type="submit" name="clone" value="Clone" />
        </div>
      </form>
    </fieldset>
    <fieldset>
      <legend>Import binfile:</legend>
      <desc>Import penpot file in binary format.</desc>

      <form method="post" enctype="multipart/form-data" action="/dbg/actions/file-import">
        <div class="row">
          <input type="file" name="file" value="" />
        </div>

        <div class="row">
          <input type="submit" name="upload" value="Upload" />
        </div>
      </form>
    </fieldset>
  </section>

  <section class="widget">
    <fieldset>
      <legend>Feature Flags for Team</legend>
      <desc>Add a feature flag to a team</desc>
      <form method="post" action="/dbg/actions/handle-team-features">
        <div class="row">
          <input type="text" style="width:300px" name="team-id" placeholder="team-id" />
        </div>
        <div class="row">
          <select type="text" style="width:100px" name="feature">
            {% for feature in supported-features %}
              <option value="{{feature}}">{{feature}}</option>
            {% endfor %}
          </select>
        </div>

        <div class="row">
          <select style="width:100px" name="action">
            <option value="">Action...</option>
            <option value="show">Show</option>
            <option value="enable">Enable</option>
            <option value="disable">Disable</option>
          </select>
        </div>

        <div class="row">
          <label for="check-feature">Skip feature check</label>
          <input id="check-feature" type="checkbox" name="skip-check" />
          <br />
          <small>
            Do not check if the feature is supported
          </small>
        </div>

        <div class="row">
          <label for="force-version">Are you sure?</label>
          <input id="force-version" type="checkbox" name="force" />
          <br />
          <small>
            This is a just a security double check for prevent non intentional submits.
          </small>
        </div>

        <div class="row">
          <input type="submit" value="Submit" />
        </div>
      </form>
    </fieldset>
  </section>
</main>
{% endblock %}
