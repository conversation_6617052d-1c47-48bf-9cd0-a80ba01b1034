<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="fatal" monitorInterval="60">
  <Appenders>
    <Console name="console" target="SYSTEM_OUT">
      <PatternLayout pattern="[%d{YYYY-MM-dd HH:mm:ss.SSS}] %level{length=1} %logger{36} - %msg%n"
                     alwaysWriteExceptions="false" />
    </Console>
  </Appenders>

  <Loggers>
    <Logger name="io.lettuce" level="error" />
    <Logger name="com.zaxxer.hikari" level="error" />
    <Logger name="org.postgresql" level="error" />
    <Logger name="app" level="info" additivity="false">
      <AppenderRef ref="console" level="info" />
    </Logger>
    <Root level="info">
      <AppenderRef ref="console" />
    </Root>
  </Loggers>
</Configuration>
