{"name": "backend", "version": "1.0.0", "license": "MPL-2.0", "author": "Kaleidos INC", "private": true, "packageManager": "yarn@4.9.2+sha512.1fc009bc09d13cfd0e19efa44cbfc2b9cf6ca61482725eb35bbc5e257e093ebf4130db6dfe15d604ff4b79efd8e1e8e99b25fa7d0a6197c9f9826358d4d65c3c", "repository": {"type": "git", "url": "https://github.com/penpot/penpot"}, "dependencies": {"luxon": "^3.4.4", "sax": "^1.4.1"}, "devDependencies": {"nodemon": "^3.1.2", "source-map-support": "^0.5.21", "ws": "^8.17.0"}, "scripts": {"fmt:clj:check": "cljfmt check --parallel=false src/ test/", "fmt:clj": "cljfmt fix --parallel=true src/ test/", "lint:clj": "clj-kondo --parallel --lint src/"}}