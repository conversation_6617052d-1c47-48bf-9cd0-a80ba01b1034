#!/usr/bin/env bash

export PENPOT_SECRET_KEY=super-secret-devenv-key
export PENPOT_HOST=devenv
export PENPOT_FLAGS="\
       $PENPOT_FLAGS \
       enable-backend-asserts \
       enable-feature-fdata-pointer-map \
       enable-feature-fdata-objects-map \
       enable-file-snapshot \
       enable-tiered-file-data-storage";

export JAVA_OPTS="
       -Djava.util.logging.manager=org.apache.logging.log4j.jul.LogManager \
       -Djdk.attach.allowAttachSelf \
       -Dlog4j2.configurationFile=log4j2-devenv.xml \
       -XX:+EnableDynamicAgentLoading \
       -XX:-OmitStackTraceInFastThrow \
       -XX:+UnlockDiagnosticVMOptions \
       -XX:+DebugNonSafepoints";

export CLOJURE_OPTIONS="-A:dev"


# Default deletion delay for devenv
export PENPOT_DELETION_DELAY="24h"

# Setup default upload media file size to 100MiB
export PENPOT_MEDIA_MAX_FILE_SIZE=104857600

# Setup default multipart upload size to 300MiB
export PENPOT_HTTP_SERVER_MAX_MULTIPART_BODY_SIZE=314572800

export AWS_ACCESS_KEY_ID=penpot-devenv
export AWS_SECRET_ACCESS_KEY=penpot-devenv
export PENPOT_OBJECTS_STORAGE_BACKEND=s3
export PENPOT_OBJECTS_STORAGE_S3_ENDPOINT=http://minio:9000
export PENPOT_OBJECTS_STORAGE_S3_BUCKET=penpot

entrypoint=${1:-app.main};
shift 1;
set -ex

clojure $CLOJURE_OPTIONS -A:dev -M -m $entrypoint "$@";
