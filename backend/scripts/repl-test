#!/usr/bin/env bash

source /home/<USER>/environ
export PENPOT_FLAGS="$PENPOT_FLAGS disable-backend-worker"

export OPTIONS="
       -A:jmx-remote -A:dev \
       -J-Djava.util.logging.manager=org.apache.logging.log4j.jul.LogManager \
       -J-Djdk.attach.allowAttachSelf \
       -J-Dlog4j2.configurationFile=log4j2-experiments.xml \
       -J-XX:-OmitStackTraceInFastThrow \
       -J-XX:+UnlockDiagnosticVMOptions \
       -J-XX:+DebugNonSafepoints \
       -J-Djdk.tracePinnedThreads=full \
       -J-XX:+UseTransparentHugePages \
       -J-XX:ReservedCodeCacheSize=1g \
       -J-Dpolyglot.engine.WarnInterpreterOnly=false \
       -J--enable-preview";

# Setup HEAP
export OPTIONS="$OPTIONS -J-Xms320g -J-Xmx320g -J-XX:+AlwaysPreTouch"

export PENPOT_HTTP_SERVER_IO_THREADS=2
export PENPOT_HTTP_SERVER_WORKER_THREADS=2

# Increase virtual thread pool size
# export OPTIONS="$OPTIONS -J-Djdk.virtualThreadScheduler.parallelism=16"

# Disable C2 Compiler
# export OPTIONS="$OPTIONS -J-XX:TieredStopAtLevel=1"

# Disable all compilers
# export OPTIONS="$OPTIONS -J-Xint"

# Setup GC
export OPTIONS="$OPTIONS -J-XX:+UseG1GC -J-Xlog:gc:logs/gc.log"

# Setup GC
#export OPTIONS="$OPTIONS -J-XX:+UseZGC -J-XX:+ZGenerational -J-Xlog:gc:logs/gc.log"

# Enable ImageMagick v7.x support
# export OPTIONS="-J-Dim4java.useV7=true $OPTIONS";

export OPTIONS_EVAL="nil"
# export OPTIONS_EVAL="(set! *warn-on-reflection* true)"

set -ex
exec clojure $OPTIONS -M -e "$OPTIONS_EVAL" -m rebel-readline.main
