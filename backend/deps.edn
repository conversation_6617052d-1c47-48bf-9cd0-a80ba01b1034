{:mvn/repos
 {"sonatype" {:url "https://oss.sonatype.org/content/repositories/snapshots/"}}

 :deps
 {penpot/common {:local/root "../common"}
  org.clojure/clojure {:mvn/version "1.12.2"}
  org.clojure/tools.namespace {:mvn/version "1.5.0"}

  com.github.luben/zstd-jni {:mvn/version "1.5.7-3"}

  io.prometheus/simpleclient {:mvn/version "0.16.0"}
  io.prometheus/simpleclient_hotspot {:mvn/version "0.16.0"}
  io.prometheus/simpleclient_jetty
  {:mvn/version "0.16.0"
   :exclusions [org.eclipse.jetty/jetty-server
                org.eclipse.jetty/jetty-servlet]}

  io.prometheus/simpleclient_httpserver {:mvn/version "0.16.0"}

  io.lettuce/lettuce-core {:mvn/version "6.7.0.RELEASE"}
  ;; Minimal dependencies required by lettuce, we need to include them
  ;; explicitly because clojure dependency management does not support
  ;; yet the BOM format.
  io.micrometer/micrometer-core {:mvn/version "1.14.2"}
  io.micrometer/micrometer-observation {:mvn/version "1.14.2"}

  java-http-clj/java-http-clj {:mvn/version "0.4.3"}
  com.google.guava/guava {:mvn/version "33.4.8-jre"}

  funcool/yetti
  {:git/tag "v11.4"
   :git/sha "ce50d42"
   :git/url "https://github.com/funcool/yetti.git"
   :exclusions [org.slf4j/slf4j-api]}

  com.github.seancorfield/next.jdbc
  {:mvn/version "1.3.1002"}
  metosin/reitit-core {:mvn/version "0.9.1"}
  nrepl/nrepl {:mvn/version "1.3.1"}

  org.postgresql/postgresql {:mvn/version "42.7.6"}
  org.xerial/sqlite-jdbc {:mvn/version "********"}

  com.zaxxer/HikariCP {:mvn/version "6.3.0"}

  io.whitfin/siphash {:mvn/version "2.0.0"}

  buddy/buddy-hashers {:mvn/version "2.0.167"}
  buddy/buddy-sign {:mvn/version "3.6.1-359"}

  com.github.ben-manes.caffeine/caffeine {:mvn/version "3.2.0"}

  org.jsoup/jsoup {:mvn/version "1.20.1"}
  org.im4java/im4java
  {:git/tag "1.4.0-penpot-2"
   :git/sha "e2b3e16"
   :git/url "https://github.com/penpot/im4java"}

  org.lz4/lz4-java {:mvn/version "1.8.0"}

  org.clojars.pntblnk/clj-ldap {:mvn/version "0.0.17"}

  dawran6/emoji {:mvn/version "0.1.5"}
  markdown-clj/markdown-clj {:mvn/version "1.12.3"}

  ;; Pretty Print specs
  pretty-spec/pretty-spec {:mvn/version "0.1.4"}
  software.amazon.awssdk/s3 {:mvn/version "2.31.55"}}

 :paths ["src" "resources" "target/classes"]
 :aliases
 {:dev
  {:extra-deps
   {com.bhauman/rebel-readline {:mvn/version "RELEASE"}
    clojure-humanize/clojure-humanize {:mvn/version "0.2.2"}
    org.clojure/data.csv {:mvn/version "RELEASE"}
    com.clojure-goes-fast/clj-async-profiler {:mvn/version "RELEASE"}
    mockery/mockery {:mvn/version "RELEASE"}}
   :extra-paths ["test" "dev"]}

  :build
  {:extra-deps
   {io.github.clojure/tools.build {:git/tag "v0.10.9" :git/sha "e405aac"}}
   :ns-default build}

  :test
  {:main-opts ["-m" "kaocha.runner"]
   :jvm-opts ["-Dlog4j2.configurationFile=log4j2-devenv-repl.xml"]
   :extra-deps {lambdaisland/kaocha {:mvn/version "1.91.1392"}}}

  :outdated
  {:extra-deps {com.github.liquidz/antq {:mvn/version "RELEASE"}}
   :main-opts ["-m" "antq.core"]}

  :jmx-remote
  {:jvm-opts ["-Dcom.sun.management.jmxremote"
              "-Dcom.sun.management.jmxremote.port=9090"
              "-Dcom.sun.management.jmxremote.rmi.port=9090"
              "-Dcom.sun.management.jmxremote.local.only=false"
              "-Dcom.sun.management.jmxremote.authenticate=false"
              "-Dcom.sun.management.jmxremote.ssl=false"
              "-Djava.rmi.server.hostname=localhost"]}

  }}
