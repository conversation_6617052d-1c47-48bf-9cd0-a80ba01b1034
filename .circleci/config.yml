version: 2.1

jobs:
  lint:
    docker:
      - image: penpotapp/devenv:latest

    working_directory: ~/repo
    resource_class: medium+

    steps:
      - checkout

      - run:
          name: "fmt check"
          working_directory: "."
          command: |
            yarn install
            yarn run fmt:clj:check

      - run:
          name: "lint clj common"
          working_directory: "."
          command: |
            yarn run lint:clj:common

      - run:
          name: "lint clj frontend"
          working_directory: "."
          command: |
            yarn run lint:clj:frontend

      - run:
          name: "lint clj backend"
          working_directory: "."
          command: |
            yarn run lint:clj:backend

      - run:
          name: "lint clj exporter"
          working_directory: "."
          command: |
            yarn run lint:clj:exporter

      - run:
          name: "lint clj library"
          working_directory: "."
          command: |
            yarn run lint:clj:library

  test-common:
    docker:
      - image: penpotapp/devenv:latest

    working_directory: ~/repo
    resource_class: medium+

    environment:
      JAVA_OPTS: -Xmx4g -Xms100m -XX:+UseSerialGC
      NODE_OPTIONS: --max-old-space-size=4096

    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "common/deps.edn"}}-{{ checksum "common/yarn.lock" }}

      - run:
          name: "JVM tests"
          working_directory: "./common"
          command: |
            clojure -M:dev:test

      - run:
          name: "NODE tests"
          working_directory: "./common"
          command: |
            yarn install
            yarn run test

      - save_cache:
          paths:
            - ~/.m2
            - ~/.yarn
            - ~/.gitlibs
            - ~/.cache/ms-playwright
          key: v1-dependencies-{{ checksum "common/deps.edn"}}-{{ checksum "common/yarn.lock" }}

  test-frontend:
    docker:
      - image: penpotapp/devenv:latest

    working_directory: ~/repo
    resource_class: medium+

    environment:
      JAVA_OPTS: -Xmx4g -Xms100m -XX:+UseSerialGC
      NODE_OPTIONS: --max-old-space-size=4096

    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "frontend/deps.edn"}}-{{ checksum "frontend/yarn.lock" }}

      - run:
          name: "install dependencies"
          working_directory: "./frontend"
          # We install playwright here because the dependent tasks
          # uses the same cache as this task so we prepopulate it
          command: |
            yarn install
            yarn run playwright install chromium

      - run:
          name: "lint scss on frontend"
          working_directory: "./frontend"
          command: |
            yarn run lint:scss

      - run:
          name: "unit tests"
          working_directory: "./frontend"
          command: |
            yarn run test

      - save_cache:
          paths:
            - ~/.m2
            - ~/.yarn
            - ~/.gitlibs
            - ~/.cache/ms-playwright
          key: v1-dependencies-{{ checksum "frontend/deps.edn"}}-{{ checksum "frontend/yarn.lock" }}

  test-library:
    docker:
      - image: penpotapp/devenv:latest

    working_directory: ~/repo
    resource_class: medium+

    environment:
      JAVA_OPTS: -Xmx6g
      NODE_OPTIONS: --max-old-space-size=4096

    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "frontend/deps.edn"}}-{{ checksum "frontend/yarn.lock" }}

      - run:
          name: Install dependencies and build
          working_directory: "./library"
          command: |
            yarn install

      - run:
          name: Build and Test
          working_directory: "./library"
          command: |
            ./scripts/build
            yarn run test

  test-components:
    docker:
      - image: penpotapp/devenv:latest

    working_directory: ~/repo
    resource_class: medium+

    environment:
      JAVA_OPTS: -Xmx6g -Xms2g
      NODE_OPTIONS: --max-old-space-size=4096

    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "frontend/deps.edn"}}-{{ checksum "frontend/yarn.lock" }}

      - run:
          name: Install dependencies
          working_directory: "./frontend"
          command: |
            yarn install
            yarn run playwright install chromium

      - run:
          name: Build Storybook
          working_directory: "./frontend"
          command: yarn run build:storybook

      - run:
          name: Serve Storybook and run tests
          working_directory: "./frontend"
          command: |
            npx concurrently -k -s first -n "SB,TEST" -c "magenta,blue" \
              "npx http-server storybook-static --port 6006 --silent" \
              "npx wait-on tcp:6006 && yarn test:storybook"

  test-integration:
    docker:
      - image: penpotapp/devenv:latest

    working_directory: ~/repo
    resource_class: large

    environment:
      JAVA_OPTS: -Xmx6g -Xms2g
      NODE_OPTIONS: --max-old-space-size=4096

    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "frontend/deps.edn"}}-{{ checksum "frontend/yarn.lock" }}

      - run:
          name: "integration tests"
          working_directory: "./frontend"
          command: |
            yarn install
            yarn run build:app:assets
            yarn run build:app
            yarn run build:app:libs
            yarn run playwright install chromium
            yarn run test:e2e -x --workers=4

  test-backend:
    docker:
      - image: penpotapp/devenv:latest
      - image: cimg/postgres:14.5
        environment:
          POSTGRES_USER: penpot_test
          POSTGRES_PASSWORD: penpot_test
          POSTGRES_DB: penpot_test
      - image: cimg/redis:7.0.5

    working_directory: ~/repo
    resource_class: medium+

    environment:
      JAVA_OPTS: -Xmx4g -Xms100m -XX:+UseSerialGC
      NODE_OPTIONS: --max-old-space-size=4096

    steps:
      - checkout

      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "backend/deps.edn" }}

      - run:
          name: "tests"
          working_directory: "./backend"
          command: |
            clojure -M:dev:test --reporter kaocha.report/documentation

          environment:
            PENPOT_TEST_DATABASE_URI: "postgresql://localhost/penpot_test"
            PENPOT_TEST_DATABASE_USERNAME: penpot_test
            PENPOT_TEST_DATABASE_PASSWORD: penpot_test
            PENPOT_TEST_REDIS_URI: "redis://localhost/1"

      - save_cache:
          paths:
            - ~/.m2
            - ~/.gitlibs
          key: v1-dependencies-{{ checksum "backend/deps.edn" }}

  test-render-wasm:
    docker:
      - image: penpotapp/devenv:latest

    working_directory: ~/repo
    resource_class: medium+
    environment:

    steps:
      - checkout

      - run:
          name: "fmt check"
          working_directory: "./render-wasm"
          command: |
            cargo fmt --check

      - run:
          name: "lint"
          working_directory: "./render-wasm"
          command: |
            ./lint

      - run:
          name: "cargo tests"
          working_directory: "./render-wasm"
          command: |
            ./test

workflows:
  penpot:
    jobs:
      - lint
      - test-frontend:
          requires:
            - lint: success

      - test-library:
          requires:
            - test-frontend: success
            - lint: success

      - test-components:
          requires:
            - test-frontend: success
            - lint: success

      - test-integration:
          requires:
            - test-frontend: success
            - lint: success

      - test-backend:
          requires:
            - lint: success

      - test-common:
          requires:
            - lint: success

      - test-render-wasm
