## Common flags:
# demo-users
# email-verification
# log-emails
# log-invitation-tokens
# login-with-github
# login-with-gitlab
# login-with-google
# login-with-ldap
# login-with-oidc
# login-with-password
# prepl-server
# registration
# secure-session-cookies
# smtp
# smtp-debug
# telemetry
# webhooks
##
## You can read more about all available flags and other
## environment variables here:
## https://help.penpot.app/technical-guide/configuration/#advanced-configuration
#
# WARNING: if you're exposing Pen<PERSON> to the internet, you should remove the flags
# 'disable-secure-session-cookies' and 'disable-email-verification'
x-flags: &penpot-flags
  PENPOT_FLAGS: disable-email-verification enable-smtp enable-prepl-server disable-secure-session-cookies

x-uri: &penpot-public-uri
  PENPOT_PUBLIC_URI: http://localhost:9001

x-body-size:
  # Max body size (30MiB); Used for plain requests, should never be
  # greater than multi-part size
  &penpot-http-body-size
  PENPOT_HTTP_SERVER_MAX_BODY_SIZE: 31457280

  # Max multipart body size (350MiB)
  PENPOT_HTTP_SERVER_MAX_MULTIPART_BODY_SIZE: 367001600

networks:
  penpot:


volumes:
  penpot_postgres_v15:
  penpot_assets:
    # penpot_traefik:
    # penpot_minio:

services:
  ## Traefik service declaration example. Consider using it if you are going to expose
  ## penpot to the internet, or a different host than `localhost`.

  # traefik:
  #   image: traefik:v3.3
  #   networks:
  #     - penpot
  #   command:
  #     - "--api.insecure=true"
  #     - "--entryPoints.web.address=:80"
  #     - "--providers.docker=true"
  #     - "--providers.docker.exposedbydefault=false"
  #     - "--entryPoints.websecure.address=:443"
  #     - "--certificatesresolvers.letsencrypt.acme.tlschallenge=true"
  #     - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL_ADDRESS>"
  #     - "--certificatesresolvers.letsencrypt.acme.storage=/traefik/acme.json"
  #   volumes:
  #     - "penpot_traefik:/traefik"
  #     - "/var/run/docker.sock:/var/run/docker.sock"
  #   ports:
  #     - "80:80"
  #     - "443:443"

  penpot-frontend:
    image: "penpotapp/frontend:${PENPOT_VERSION:-latest}"
    restart: always
    ports:
      - 9001:8080

    volumes:
      - penpot_assets:/opt/data/assets

    depends_on:
      - penpot-backend
      - penpot-exporter

    networks:
      - penpot
      # labels:
      # - "traefik.enable=true"

      # ## HTTPS: example of labels for the case where penpot will be exposed to the
      # ## internet with HTTPS using traefik.

      # - "traefik.http.routers.penpot-https.rule=Host(`<DOMAIN_NAME>`)"
      # - "traefik.http.routers.penpot-https.entrypoints=websecure"
      # - "traefik.http.routers.penpot-https.tls.certresolver=letsencrypt"
      # - "traefik.http.routers.penpot-https.tls=true"

    environment:
      <<: [ *penpot-flags, *penpot-http-body-size ]

  penpot-backend:
    image: "penpotapp/backend:${PENPOT_VERSION:-latest}"
    restart: always

    volumes:
      - penpot_assets:/opt/data/assets

    depends_on:
      penpot-postgres:
        condition: service_healthy
      penpot-valkey:
        condition: service_healthy

    networks:
      - penpot

    ## Configuration envronment variables for the backend container.

    environment:
      <<: [ *penpot-flags, *penpot-public-uri, *penpot-http-body-size ]

      ## Penpot SECRET KEY. It serves as a master key from which other keys for subsystems
      ## (eg http sessions, or invitations) are derived.
      ##
      ## If you leave it commented, all created sessions and invitations will
      ## become invalid on container restart.
      ##
      ## If you going to uncomment this, we recommend to use a trully randomly generated
      ## 512 bits base64 encoded string here.  You can generate one with:
      ##
      ## python3 -c "import secrets; print(secrets.token_urlsafe(64))"

      # PENPOT_SECRET_KEY: my-insecure-key

      ## The PREPL host. Mainly used for external programatic access to penpot backend
      ## (example: admin). By default it will listen on `localhost` but if you are going to use
      ## the `admin`, you will need to uncomment this and set the host to `0.0.0.0`.

      # PENPOT_PREPL_HOST: 0.0.0.0

      ## Database connection parameters. Don't touch them unless you are using custom
      ## postgresql connection parameters.

      PENPOT_DATABASE_URI: postgresql://penpot-postgres/penpot
      PENPOT_DATABASE_USERNAME: penpot
      PENPOT_DATABASE_PASSWORD: penpot

      ## Valkey (or previously redis) is used for the websockets notifications. Don't touch
      ## unless the valkey container has different parameters or different name.

      PENPOT_REDIS_URI: redis://penpot-valkey/0

      ## Default configuration for assets storage: using filesystem based with all files
      ## stored in a docker volume.

      PENPOT_ASSETS_STORAGE_BACKEND: assets-fs
      PENPOT_STORAGE_ASSETS_FS_DIRECTORY: /opt/data/assets

      ## Also can be configured to to use a S3 compatible storage
      ## service like MiniIO. Look below for minio service setup.

      # AWS_ACCESS_KEY_ID: <KEY_ID>
      # AWS_SECRET_ACCESS_KEY: <ACCESS_KEY>
      # PENPOT_ASSETS_STORAGE_BACKEND: assets-s3
      # PENPOT_STORAGE_ASSETS_S3_ENDPOINT: http://penpot-minio:9000
      # PENPOT_STORAGE_ASSETS_S3_BUCKET: <BUKET_NAME>

      ## Telemetry. When enabled, a periodical process will send anonymous data about this
      ## instance. Telemetry data will enable us to learn how the application is used,
      ## based on real scenarios. If you want to help us, please leave it enabled. You can
      ## audit what data we send with the code available on github.

      PENPOT_TELEMETRY_ENABLED: "true"
      PENPOT_TELEMETRY_REFERER: "compose"

      ## Example SMTP/Email configuration. By default, emails are sent to the mailcatch
      ## service, but for production usage it is recommended to setup a real SMTP
      ## provider. Emails are used to confirm user registrations & invitations. Look below
      ## how the mailcatch service is configured.

      PENPOT_SMTP_DEFAULT_FROM: <EMAIL>
      PENPOT_SMTP_DEFAULT_REPLY_TO: <EMAIL>
      PENPOT_SMTP_HOST: penpot-mailcatch
      PENPOT_SMTP_PORT: 1025
      PENPOT_SMTP_USERNAME:
      PENPOT_SMTP_PASSWORD:
      PENPOT_SMTP_TLS: "false"
      PENPOT_SMTP_SSL: "false"

  penpot-exporter:
    image: "penpotapp/exporter:${PENPOT_VERSION:-latest}"
    restart: always

    depends_on:
      penpot-valkey:
        condition: service_healthy

    networks:
      - penpot

    environment:
      # Don't touch it; this uses an internal docker network to
      # communicate with the frontend.
      PENPOT_PUBLIC_URI: http://penpot-frontend:8080

      ## Valkey (or previously Redis) is used for the websockets notifications.
      PENPOT_REDIS_URI: redis://penpot-valkey/0

  penpot-postgres:
    image: "postgres:15"
    restart: always
    stop_signal: SIGINT

    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U penpot" ]
      interval: 2s
      timeout: 10s
      retries: 5
      start_period: 2s

    volumes:
      - penpot_postgres_v15:/var/lib/postgresql/data

    networks:
      - penpot

    environment:
      - POSTGRES_INITDB_ARGS=--data-checksums
      - POSTGRES_DB=penpot
      - POSTGRES_USER=penpot
      - POSTGRES_PASSWORD=penpot

  penpot-valkey:
    image: valkey/valkey:8.1
    restart: always

    healthcheck:
      test: [ "CMD-SHELL", "valkey-cli ping | grep PONG" ]
      interval: 1s
      timeout: 3s
      retries: 5
      start_period: 3s

    networks:
      - penpot

  ## A mailcatch service, used as temporal SMTP server. You can access via HTTP to the
  ## port 1080 for read all emails the penpot platform has sent. Should be only used as a
  ## temporal solution while no real SMTP provider is configured.

  penpot-mailcatch:
    image: sj26/mailcatcher:latest
    restart: always
    expose:
      - '1025'
    ports:
      - "1080:1080"
    networks:
      - penpot
  ## Example configuration of MiniIO (S3 compatible object storage service); If you don't
  ## have preference, then just use filesystem, this is here just for the completeness.

  # minio:
  #   image: "minio/minio:latest"
  #   command: minio server /mnt/data --console-address ":9001"
  #   restart: always
  #
  #   volumes:
  #     - "penpot_minio:/mnt/data"
  #
  #   environment:
  #     - MINIO_ROOT_USER=minioadmin
  #     - MINIO_ROOT_PASSWORD=minioadmin
  #
  #   ports:
  #     - 9000:9000
  #     - 9001:9001
