description: Create a report to help us improve
labels: ["bug"]
name: Bug report
title: "bug: "
body:
  - type: markdown
    attributes:
      value: |
        ## Before you start

        Please search our [existing issues](https://github.com/penpot/penpot/issues) and open [pull requests](https://github.com/penpot/penpot/pulls) to lessen the change of filing duplicate issues or feature requests. Thank you.

        ---
  - type: textarea
    attributes:
      label: Steps To Reproduce
      description: Steps to reproduce the behavior.
      placeholder: |
        Steps to reproduce the behavior:
        1.  Go to '...'
        2.  Click on '....'
        3.  Scroll down to '....'
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      description: A clear and concise description of what you expected to happen.
      label: Expected behavior
    validations:
      required: true
  - type: textarea
    id: actual
    attributes:
      description: A clear and concise description of what happens instead; what the bug is.
      label: Actual behavior
    validations:
      required: true
  - type: textarea
    id: screenshots
    attributes:
      description: If applicable, add screenshots to help explain your problem.
      label: Screenshots or video
  - type: textarea
    id: desktop
    attributes:
      label: Desktop (please complete the following information)
      placeholder: |
        - OS (e.g. iOS):
        - Browser & version (e.g. Chrome 89.0):
  - type: textarea
    id: mobile
    attributes:
      label: Smartphone (please complete the following information)
      placeholder: |
        - Device & model (e.g. iPhone 6):
        - OS & version (e.g. iOS 8.1):
        - Browser & version (e.g. stock browser 22):
  - type: textarea
    id: environment
    attributes:
      label: Environment (please complete the following information)
      placeholder: |
        - Host (e.g. https://design.penpot.app, local instance):

        *If self-hosted:*
        - OS Version (e.g. Ubuntu 16.04):
        - Docker / Docker-compose version (e.g. Docker version 18.03.0-ce, build 0520e24):
        - Image version (e.g. Alpine):

        Docker commands or docker-compose file (if possible and if proceed.x):
        ```

        ```
  - type: textarea
    id: frontend-trace
    attributes:
      label: Frontend Stack Trace
      render: console
  - type: textarea
    id: backend-trace
    attributes:
      label: Backend Stack Trace
      render: console
  - type: textarea
    id: additional-context
    attributes:
      label: Additional context
      description: Any other context about the problem.
