name: Build and Upload Penpot Bundle

on:
  # Create bundle from manual action
  workflow_dispatch:
    inputs:
      gh_ref:
        description: 'Name of the branch'
        type: string
        required: true
        default: 'develop'
      build_wasm:
        description: 'BUILD_WASM. Valid values: yes, no'
        type: string
        required: false
        default: 'yes'
      build_storybook:
        description: 'BUILD_STORYBOOK. Valid values: yes, no'
        type: string
        required: false
        default: 'yes'
  workflow_call:
    inputs:
      gh_ref:
        description: 'Name of the branch'
        type: string
        required: true
        default: 'develop'
      build_wasm:
        description: 'BUILD_WASM. Valid values: yes, no'
        type: string
        required: false
        default: 'yes'
      build_storybook:
        description: 'BUILD_STORYBOOK. Valid values: yes, no'
        type: string
        required: false
        default: 'yes'

jobs:
  build-bundle:
    name: Build and Upload Penpot Bundle
    runs-on: ubuntu-24.04
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_DEFAULT_REGION: ${{ secrets.AWS_REGION }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ inputs.gh_ref }}

      - name: Extract some useful variables
        id: vars
        run: |
          echo "commit_hash=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
          echo "gh_ref=${{ inputs.gh_ref || github.ref_name }}" >> $GITHUB_OUTPUT

      - name: Run manage.sh build-bundle from host
        env:
          BUILD_WASM: ${{ inputs.build_wasm }}
          BUILD_STORYBOOK: ${{ inputs.build_storybook }}
        run: ./manage.sh build-bundle

      - name: Prepare directories for zipping
        run: |
          mkdir zips
          mv bundles penpot

      - name: Create zip bundle
        run: |
          echo "📦 Packaging Penpot bundle..."
          zip -r zips/penpot.zip penpot

      - name: Upload Penpot bundle to S3
        if: github.ref_type == 'branch'
        run: |
          aws s3 cp zips/penpot.zip s3://${{ secrets.S3_BUCKET }}/penpot-${{ steps.vars.outputs.gh_ref }}-latest.zip
          aws s3 cp zips/penpot.zip s3://${{ secrets.S3_BUCKET }}/penpot-${{ steps.vars.outputs.commit_hash }}.zip

      - name: Upload Penpot bundle to S3
        if: github.ref_type == 'tag'
        run: |
          aws s3 cp zips/penpot.zip s3://${{ secrets.S3_BUCKET }}/penpot-${{ steps.vars.outputs.gh_ref }}.zip

      - name: Notify Mattermost
        if: failure()
        uses: mattermost/action-mattermost-notify@master
        with:
          MATTERMOST_WEBHOOK_URL: ${{ secrets.MATTERMOST_WEBHOOK }}
          TEXT: |
            ❌ *[PENPOT] Error during the execution of the job*
            📄 Triggered from ref: `${{ steps.vars.outputs.gh_ref }}`
            🔗 Run: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
